name: 'Sync Docs to pianorhythm-docs'

on:
  push:
    branches: ["main", "master"]
    paths:
      - "docs/**"
  
  workflow_dispatch:

concurrency:
  group: sync_docs
  cancel-in-progress: true

jobs:
  sync-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source repository
        uses: actions/checkout@v4

      - name: Push docs to pianorhythm-docs repository
        uses: nkoppel/push-files-to-another-repository@v1.1.4
        env:
          API_TOKEN_GITHUB: ${{ secrets.API_TOKEN_GITHUB_PAT }}
        with:
          source-files: 'docs/**'
          destination-branch: 'main'
          destination-username: 'PianoRhythm'
          destination-repository: 'pianorhythm-docs'
          destination-directory: 'docs/community/technical-documentation'
          commit-email: '<EMAIL>'
          commit-message: 'Update development docs from pianorhythm-ssr@ORIGIN_COMMIT'
