# PianoRhythm Server

A high-performance, real-time multiplayer piano gaming server built with Rust and Actix Web. This server powers the PianoRhythm platform, enabling synchronized piano gameplay, real-time communication, and comprehensive user management.

## 🎹 Overview

PianoRhythm Server is the backend infrastructure for a multiplayer piano gaming platform that supports:

- **Real-time multiplayer piano rooms** with WebSocket connections
- **User authentication and authorization** with JWT tokens
- **Room management** with various room types (public, private, pro-only)
- **Chat system** with moderation capabilities
- **Billing integration** with <PERSON><PERSON> for premium memberships
- **Analytics and monitoring** with comprehensive logging
- **Bot integration** for enhanced gameplay experiences

## 🚀 Quick Start

### Prerequisites

- Rust 1.77+ (see `rust-toolchain.toml`)
- Redis server
- MongoDB database
- Docker (optional, for containerized deployment)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/PianoRhythm/pianorhythm-server.git
   cd pianorhythm-server
   ```

2. **Install dependencies**
   ```bash
   cargo build
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start required services**
   ```bash
   # Start Redis
   ./docker-start-redis.cmd
   
   # Start Seq logging (optional)
   ./docker-start-seq.cmd
   ```

5. **Run the server**
   ```bash
   cargo run
   ```

### Running Tests

```bash
# Unit tests
./run-unit-tests.cmd

# Integration tests
./run-integration-tests.cmd
```

## 🏗️ Architecture

The server is built using a modern Rust architecture with the following key components:

- **Actix Web** - High-performance web framework
- **Actor System** - Message-driven architecture for scalability
- **Redis** - State management and pub/sub messaging
- **MongoDB** - Persistent data storage
- **WebSocket** - Real-time client communication
- **Protobuf** - Efficient binary message serialization

## 📚 Documentation

Comprehensive technical documentation is available in the [`docs/`](./docs/) directory:

- **[Architecture Overview](./docs/architecture/overview.md)** - System design and components
- **[API Documentation](./docs/api/README.md)** - REST and WebSocket APIs
- **[Deployment Guide](./docs/deployment/docker.md)** - Docker and Kubernetes deployment
- **[Development Setup](./docs/development/setup.md)** - Local development environment
- **[Testing Guide](./docs/development/testing.md)** - Testing strategies and tools

## 🔧 Configuration

The server uses environment variables for configuration. Key settings include:

- `REDIS_URL` - Redis connection string
- `MONGODB_URL` - MongoDB connection string
- `JWT_SECRET` - JWT signing secret
- `STRIPE_SECRET_KEY` - Stripe API key for billing
- `SERVER_NAME` - Server instance identifier

See [`docs/deployment/environment.md`](./docs/deployment/environment.md) for complete configuration reference.

## 🚢 Deployment

### Docker

```bash
docker build -t pianorhythm-server .
docker run -p 8080:8080 pianorhythm-server
```

### Kubernetes

The project includes comprehensive Kubernetes manifests in the `kubernetes/` directory:

```bash
kubectl apply -f kubernetes/
```

See [`docs/deployment/kubernetes.md`](./docs/deployment/kubernetes.md) for detailed deployment instructions.

## 🧪 Testing

The project includes comprehensive test coverage:

- **Unit Tests** - Individual component testing
- **Integration Tests** - End-to-end API testing
- **Load Tests** - Performance and scalability testing

## 📊 Monitoring

The server includes built-in monitoring and observability:

- **Prometheus metrics** - Performance and health metrics
- **Structured logging** - JSON-formatted logs with Seq integration
- **Sentry integration** - Error tracking and performance monitoring
- **Health checks** - Kubernetes-ready health endpoints

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/development/contributing.md) for details on:

- Code style and standards
- Development workflow
- Testing requirements
- Pull request process

## 📄 License

This project is proprietary software. See `NOTICE.txt` for third-party licenses and attributions.

## 🔗 Related Projects

- **PianoRhythm Client** - Web client application
- **PianoRhythm Mobile** - Mobile applications
- **PianoRhythm SSR** - Server-side rendering service

## 📞 Support

For technical support and questions:

- **Documentation** - Check the [`docs/`](./docs/) directory
- **Issues** - Create a GitHub issue for bugs and feature requests
- **Discord** - Join our development Discord server

---

Built with ❤️ by the PianoRhythm team
